package org.dromara.common.mybatis.core.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.QuestionCommentVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T09:34:24+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class QuestionCommentToQuestionCommentVoMapper__1Impl implements QuestionCommentToQuestionCommentVoMapper__1 {

    @Override
    public QuestionCommentVo convert(QuestionComment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionCommentVo questionCommentVo = new QuestionCommentVo();

        questionCommentVo.setCommentId( arg0.getCommentId() );
        questionCommentVo.setContent( arg0.getContent() );
        questionCommentVo.setCreateTime( arg0.getCreateTime() );
        questionCommentVo.setIpAddress( arg0.getIpAddress() );
        questionCommentVo.setLikeCount( arg0.getLikeCount() );
        questionCommentVo.setParentId( arg0.getParentId() );
        questionCommentVo.setQuestionId( arg0.getQuestionId() );
        questionCommentVo.setRemark( arg0.getRemark() );
        questionCommentVo.setReplyCount( arg0.getReplyCount() );
        questionCommentVo.setSort( arg0.getSort() );
        questionCommentVo.setStatus( arg0.getStatus() );
        questionCommentVo.setUpdateTime( arg0.getUpdateTime() );
        questionCommentVo.setUserId( arg0.getUserId() );

        return questionCommentVo;
    }

    @Override
    public QuestionCommentVo convert(QuestionComment arg0, QuestionCommentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCommentId( arg0.getCommentId() );
        arg1.setContent( arg0.getContent() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setIpAddress( arg0.getIpAddress() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setReplyCount( arg0.getReplyCount() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
