package org.dromara.app.domain.vo;

import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.JobCategory;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T09:34:34+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class JobCategoryVoToJobCategoryMapper__1Impl implements JobCategoryVoToJobCategoryMapper__1 {

    @Override
    public JobCategory convert(JobCategoryVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        JobCategory jobCategory = new JobCategory();

        if ( arg0.getCreateTime() != null ) {
            jobCategory.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            jobCategory.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        jobCategory.setColor( arg0.getColor() );
        jobCategory.setDescription( arg0.getDescription() );
        jobCategory.setIcon( arg0.getIcon() );
        jobCategory.setId( arg0.getId() );
        jobCategory.setName( arg0.getName() );
        jobCategory.setSortOrder( arg0.getSortOrder() );
        jobCategory.setStatus( arg0.getStatus() );

        return jobCategory;
    }

    @Override
    public JobCategory convert(JobCategoryVo arg0, JobCategory arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setColor( arg0.getColor() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
