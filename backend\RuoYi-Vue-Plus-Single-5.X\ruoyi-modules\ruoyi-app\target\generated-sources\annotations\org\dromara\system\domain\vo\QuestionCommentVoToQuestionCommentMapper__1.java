package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.QuestionComment;
import org.dromara.common.mybatis.core.domain.QuestionCommentToQuestionCommentVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {QuestionCommentToQuestionCommentVoMapper__1.class,QuestionCommentToQuestionCommentVoMapper__1.class},
    imports = {}
)
public interface QuestionCommentVoToQuestionCommentMapper__1 extends BaseMapper<QuestionCommentVo, QuestionComment> {
}
