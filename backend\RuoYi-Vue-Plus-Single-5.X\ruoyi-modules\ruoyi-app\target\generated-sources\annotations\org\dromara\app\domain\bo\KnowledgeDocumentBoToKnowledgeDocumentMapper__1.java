package org.dromara.app.domain.bo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeDocument;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {},
    imports = {}
)
public interface KnowledgeDocumentBoToKnowledgeDocumentMapper__1 extends BaseMapper<KnowledgeDocumentBo, KnowledgeDocument> {
}
