package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.InterviewModeVo;
import org.dromara.app.domain.vo.InterviewModeVoToInterviewModeMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {InterviewModeVoToInterviewModeMapper__1.class},
    imports = {}
)
public interface InterviewModeToInterviewModeVoMapper__1 extends BaseMapper<InterviewMode, InterviewModeVo> {
}
