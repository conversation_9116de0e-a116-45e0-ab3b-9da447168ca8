package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__144;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionCommentBoToQuestionCommentMapper__1;
import org.dromara.system.domain.vo.QuestionCommentVo;
import org.dromara.system.domain.vo.QuestionCommentVoToQuestionCommentMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__144.class,
    uses = {QuestionCommentBoToQuestionCommentMapper__1.class,QuestionCommentVoToQuestionCommentMapper__1.class},
    imports = {}
)
public interface QuestionCommentToQuestionCommentVoMapper__1 extends BaseMapper<QuestionComment, QuestionCommentVo> {
}
