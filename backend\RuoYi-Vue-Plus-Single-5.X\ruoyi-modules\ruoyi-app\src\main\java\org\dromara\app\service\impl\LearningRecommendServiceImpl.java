package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.*;
import org.dromara.app.domain.vo.RecommendationResponseVo;
import org.dromara.app.mapper.*;
import org.dromara.app.service.ILearningRecommendService;
import org.dromara.common.mybatis.core.domain.QuestionBank;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Date;

/**
 * 学习推荐服务实现类
 * 基于用户能力评估和学习历史，提供个性化的学习资源推荐
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LearningRecommendServiceImpl implements ILearningRecommendService {

    private final VideoMapper videoMapper;
    private final QuestionBankMapper questionBankMapper;
    private final BookMapper bookMapper;
    private final InterviewResultMapper interviewResultMapper;
    private final LearningProgressMapper learningProgressMapper;
    private final UserBehaviorMapper userBehaviorMapper;

    // 能力维度映射
    private static final Map<String, String> CAPABILITY_MAPPING = Map.of(
            "professionalKnowledge", "专业知识",
            "communicationSkills", "表达能力",
            "logicalThinking", "逻辑思维",
            "problemSolving", "问题解决",
            "innovation", "创新能力",
            "pressureHandling", "抗压能力"
    );

    // 推荐算法版本
    private static final String ALGORITHM_VERSION = "v1.0.0";

    @Override
    public RecommendationResponseVo getRecommendedVideos(Long userId, Integer pageNum, Integer pageSize, String searchQuery) {
        log.info("获取用户{}的推荐视频，页码：{}，大小：{}，搜索：{}", userId, pageNum, pageSize, searchQuery);

        // 获取用户能力短板
        Map<String, Double> weaknesses = calculateUserWeaknesses(userId);

        // 构建查询条件
        LambdaQueryWrapper<Video> queryWrapper = new LambdaQueryWrapper<>();

        // 搜索条件
        if (StrUtil.isNotBlank(searchQuery)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(Video::getTitle, searchQuery)
                    .or().like(Video::getDescription, searchQuery)
                    .or().like(Video::getTags, searchQuery));
        }

        // 状态过滤
        queryWrapper.eq(Video::getStatus, "1"); // 只查询启用状态的视频

        // 排序：优先推荐与用户薄弱能力相关的视频
        queryWrapper.orderByDesc(Video::getCreateTime);

        // 分页查询
        int offset = (pageNum - 1) * pageSize;
        List<Video> videos = videoMapper.selectList(queryWrapper);

        // 计算推荐优先级并排序
        List<Video> sortedVideos = videos.stream()
                .sorted((v1, v2) -> {
                    Double priority1 = calculateRecommendationPriority(userId, "video", v1.getId());
                    Double priority2 = calculateRecommendationPriority(userId, "video", v2.getId());
                    return priority2.compareTo(priority1);
                })
                .skip(offset)
                .limit(pageSize)
                .collect(Collectors.toList());

        // 转换为VO
        List<RecommendationResponseVo.RecommendationItemVo> items = sortedVideos.stream()
                .map(video -> convertVideoToRecommendationItem(video, userId, weaknesses))
                .collect(Collectors.toList());

        // 构建响应
        RecommendationResponseVo response = new RecommendationResponseVo();
        response.setItems(items);
        response.setTotal((long) videos.size());
        response.setPageNum(pageNum);
        response.setPageSize(pageSize);
        response.setAlgorithmVersion(ALGORITHM_VERSION);
        response.setGeneratedAt(LocalDateTime.now());

        return response;
    }

    @Override
    public RecommendationResponseVo getRecommendedQuestionBanks(Long userId, Integer pageNum, Integer pageSize, String searchQuery) {
        log.info("获取用户{}的推荐题库，页码：{}，大小：{}，搜索：{}", userId, pageNum, pageSize, searchQuery);

        // 获取用户能力短板
        Map<String, Double> weaknesses = calculateUserWeaknesses(userId);

        // 构建查询条件
        LambdaQueryWrapper<QuestionBank> queryWrapper = new LambdaQueryWrapper<>();

        // 搜索条件
        if (StrUtil.isNotBlank(searchQuery)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(QuestionBank::getTitle, searchQuery)
                    .or().like(QuestionBank::getDescription, searchQuery)
                    .or().like(QuestionBank::getCategories, searchQuery));
        }

        // 状态过滤
        queryWrapper.eq(QuestionBank::getStatus, "0");
        queryWrapper.orderByDesc(QuestionBank::getCreateTime);

        // 分页查询
        int offset = (pageNum - 1) * pageSize;
        List<QuestionBank> questionBanks = questionBankMapper.selectList(queryWrapper);

        // 计算推荐优先级并排序
        List<QuestionBank> sortedQuestionBanks = questionBanks.stream()
                .sorted((q1, q2) -> {
                    Double priority1 = calculateRecommendationPriority(userId, "question-bank", q1.getBankId());
                    Double priority2 = calculateRecommendationPriority(userId, "question-bank", q2.getBankId());
                    return priority2.compareTo(priority1);
                })
                .skip(offset)
                .limit(pageSize)
                .collect(Collectors.toList());

        // 转换为VO
        List<RecommendationResponseVo.RecommendationItemVo> items = sortedQuestionBanks.stream()
                .map(questionBank -> convertQuestionBankToRecommendationItem(questionBank, userId, weaknesses))
                .collect(Collectors.toList());

        // 构建响应
        RecommendationResponseVo response = new RecommendationResponseVo();
        response.setItems(items);
        response.setTotal((long) questionBanks.size());
        response.setPageNum(pageNum);
        response.setPageSize(pageSize);
        response.setAlgorithmVersion(ALGORITHM_VERSION);
        response.setGeneratedAt(LocalDateTime.now());

        return response;
    }

    @Override
    public RecommendationResponseVo getRecommendedBooks(Long userId, Integer pageNum, Integer pageSize, String searchQuery) {
        log.info("获取用户{}的推荐书籍，页码：{}，大小：{}，搜索：{}", userId, pageNum, pageSize, searchQuery);

        // 获取用户能力短板
        Map<String, Double> weaknesses = calculateUserWeaknesses(userId);

        // 构建查询条件
        LambdaQueryWrapper<Book> queryWrapper = new LambdaQueryWrapper<>();

        // 搜索条件
        if (StrUtil.isNotBlank(searchQuery)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(Book::getTitle, searchQuery)
                    .or().like(Book::getDescription, searchQuery)
                    .or().like(Book::getTags, searchQuery));
        }

        // 状态过滤
        queryWrapper.eq(Book::getStatus, "1");
        queryWrapper.orderByDesc(Book::getCreateTime);

        // 分页查询
        int offset = (pageNum - 1) * pageSize;
        List<Book> books = bookMapper.selectList(queryWrapper);

        // 计算推荐优先级并排序
        List<Book> sortedBooks = books.stream()
                .sorted((b1, b2) -> {
                    Double priority1 = calculateRecommendationPriority(userId, "book", b1.getId());
                    Double priority2 = calculateRecommendationPriority(userId, "book", b2.getId());
                    return priority2.compareTo(priority1);
                })
                .skip(offset)
                .limit(pageSize)
                .collect(Collectors.toList());

        // 转换为VO
        List<RecommendationResponseVo.RecommendationItemVo> items = sortedBooks.stream()
                .map(book -> convertBookToRecommendationItem(book, userId, weaknesses))
                .collect(Collectors.toList());

        // 构建响应
        RecommendationResponseVo response = new RecommendationResponseVo();
        response.setItems(items);
        response.setTotal((long) books.size());
        response.setPageNum(pageNum);
        response.setPageSize(pageSize);
        response.setAlgorithmVersion(ALGORITHM_VERSION);
        response.setGeneratedAt(LocalDateTime.now());

        return response;
    }

    @Override
    public Map<String, Object> getUserCapabilities(Long userId) {
        log.info("获取用户{}的能力评估数据", userId);

        Map<String, Object> result = new HashMap<>();

        // 模拟用户能力数据（实际应该从面试结果等数据计算）
        Map<String, Double> capabilities = new HashMap<>();
        capabilities.put("professionalKnowledge", 75.0);
        capabilities.put("communicationSkills", 68.0);
        capabilities.put("logicalThinking", 85.0);
        capabilities.put("problemSolving", 72.0);
        capabilities.put("innovation", 60.0);
        capabilities.put("pressureHandling", 78.0);

        result.put("capabilities", capabilities);

        // 计算薄弱环节
        List<Map<String, Object>> weaknesses = capabilities.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .limit(3)
                .map(entry -> {
                    Map<String, Object> weakness = new HashMap<>();
                    weakness.put("key", entry.getKey());
                    weakness.put("name", CAPABILITY_MAPPING.get(entry.getKey()));
                    weakness.put("value", entry.getValue());
                    weakness.put("improvement", 100 - entry.getValue());
                    return weakness;
                })
                .collect(Collectors.toList());

        result.put("weaknesses", weaknesses);
        result.put("lastUpdated", LocalDateTime.now());

        return result;
    }

    @Override
    public Map<String, Object> getRecommendationStatistics(Long userId) {
        log.info("获取用户{}的推荐统计信息", userId);

        Map<String, Object> statistics = new HashMap<>();

        // 统计各类型推荐数量（简化实现）
        statistics.put("totalRecommendations", 50);
        statistics.put("videoRecommendations", 20);
        statistics.put("questionBankRecommendations", 15);
        statistics.put("bookRecommendations", 15);
        statistics.put("highPriorityCount", 12);
        statistics.put("mediumPriorityCount", 23);
        statistics.put("lowPriorityCount", 15);
        statistics.put("lastRefreshTime", LocalDateTime.now().minusHours(2));

        return statistics;
    }

    @Override
    public void refreshUserRecommendations(Long userId) {
        log.info("刷新用户{}的推荐算法", userId);
        // 这里可以实现推荐算法的刷新逻辑
        // 例如：重新计算用户画像、更新推荐权重等
    }

    @Override
    public void recordRecommendationFeedback(Long userId, String resourceType, Long resourceId, String action) {
        log.info("记录用户{}对资源{}的反馈：{}-{}", userId, resourceId, resourceType, action);
        // 这里可以实现反馈记录逻辑，用于优化推荐算法
    }

    @Override
    public Map<String, Double> calculateUserWeaknesses(Long userId) {
        // 简化实现：基于模拟数据计算用户薄弱环节
        Map<String, Double> weaknesses = new HashMap<>();
        weaknesses.put("communicationSkills", 68.0);
        weaknesses.put("innovation", 60.0);
        weaknesses.put("problemSolving", 72.0);
        return weaknesses;
    }

    @Override
    public Double calculateRecommendationPriority(Long userId, String resourceType, Long resourceId) {
        // 简化的推荐优先级计算算法
        // 实际应该考虑：用户能力短板匹配度、资源质量、用户历史行为等

        Map<String, Double> weaknesses = calculateUserWeaknesses(userId);

        // 基础分数
        double baseScore = 50.0;

        // 根据资源类型调整
        switch (resourceType) {
            case "video":
                baseScore += 10.0; // 视频更受欢迎
                break;
            case "question-bank":
                baseScore += 15.0; // 题库练习效果好
                break;
            case "book":
                baseScore += 5.0; // 书籍学习周期长
                break;
        }

        // 添加随机因子模拟个性化
        baseScore += Math.random() * 20;

        return Math.min(100.0, baseScore);
    }

    @Override
    public Map<String, Object> getPersonalizedAlgorithmConfig(Long userId) {
        Map<String, Object> config = new HashMap<>();
        config.put("learningStyle", "visual"); // 学习风格
        config.put("difficultyPreference", "medium"); // 难度偏好
        config.put("timeAvailability", "2-3hours"); // 可用时间
        config.put("focusAreas", Arrays.asList("技术专业", "沟通表达")); // 关注领域
        return config;
    }

    /**
     * 将视频转换为推荐项VO
     */
    private RecommendationResponseVo.RecommendationItemVo convertVideoToRecommendationItem(
            Video video, Long userId, Map<String, Double> weaknesses) {

        RecommendationResponseVo.RecommendationItemVo item = new RecommendationResponseVo.RecommendationItemVo();

        item.setId(video.getId());
        item.setTitle(video.getTitle());
        item.setDescription(video.getDescription());
        item.setType("video");
        item.setDifficulty(video.getDifficulty() != null ? video.getDifficulty() : "中等");
        item.setCategory(video.getCategory() != null ? video.getCategory() : "技术专业");
        item.setCover(video.getThumbnail() != null ? video.getThumbnail() : "/static/default-video.jpg");
        item.setRating(video.getRating() != null ? video.getRating() : BigDecimal.valueOf(4.5));
        item.setDuration(video.getDuration() != null ? video.getDuration() : "未知");
        item.setInstructor(video.getInstructor() != null ? video.getInstructor() : "专业讲师");
        item.setStudentCount(video.getStudentCount() != null ? video.getStudentCount() : 0);
        item.setPrice(video.getPrice() != null ? video.getPrice() : BigDecimal.ZERO);
        item.setOriginalPrice(video.getPrice() != null ? video.getPrice().multiply(BigDecimal.valueOf(1.5)) : BigDecimal.valueOf(99));
        item.setCreateTime(convertToLocalDateTime(video.getCreateTime()));
        item.setUpdateTime(convertToLocalDateTime(video.getUpdateTime()));

        // 设置标签
        if (StrUtil.isNotBlank(video.getTags())) {
            JSONArray objects = JSONUtil.parseArray(video.getTags());
            item.setTags(objects.stream()
                    .map(Object::toString)
                    .collect(Collectors.toList()));
        }

        // 计算推荐优先级
        Double priorityScore = calculateRecommendationPriority(userId, "video", video.getId());
        item.setPriorityScore(priorityScore);
        item.setPriority(getPriorityLevel(priorityScore));

        // 设置目标能力和推荐原因
        setTargetCapabilityAndReason(item, weaknesses);

        // 设置是否收藏（简化实现）
        item.setIsBookmarked(false);

        return item;
    }

    /**
     * 将题库转换为推荐项VO
     */
    private RecommendationResponseVo.RecommendationItemVo convertQuestionBankToRecommendationItem(
            QuestionBank questionBank, Long userId, Map<String, Double> weaknesses) {

        RecommendationResponseVo.RecommendationItemVo item = new RecommendationResponseVo.RecommendationItemVo();

        item.setId(questionBank.getBankId());
        item.setTitle(questionBank.getTitle());
        item.setDescription(questionBank.getDescription());
        item.setType("question-bank");
        item.setDifficulty(convertDifficultyLevel(questionBank.getDifficulty()));
        item.setCategory(questionBank.getCategories() != null ? questionBank.getCategories() : "算法讲解");
        item.setCover(questionBank.getIcon() != null ? questionBank.getIcon() : "/static/default-question.jpg");
        item.setRating(BigDecimal.valueOf(4.5)); // 题库没有评分字段，使用默认值
        item.setQuestionCount(questionBank.getTotalQuestions());
        item.setEstimatedTime(calculateEstimatedTime(questionBank.getTotalQuestions()));
        item.setCreateTime(convertToLocalDateTime(questionBank.getCreateTime()));
        item.setUpdateTime(convertToLocalDateTime(questionBank.getUpdateTime()));

        // 设置标签
        if (StrUtil.isNotBlank(questionBank.getCategories())) {
            JSONArray objects = JSONUtil.parseArray(questionBank.getCategories());
            item.setTags(objects.stream()
                    .map(Object::toString)
                    .collect(Collectors.toList()));
        }

        // 计算用户进度（简化实现）
        item.setCompletedCount(0);
        item.setProgress(BigDecimal.ZERO);

        // 计算推荐优先级
        Double priorityScore = calculateRecommendationPriority(userId, "question-bank", questionBank.getBankId());
        item.setPriorityScore(priorityScore);
        item.setPriority(getPriorityLevel(priorityScore));

        // 设置目标能力和推荐原因
        setTargetCapabilityAndReason(item, weaknesses);

        // 设置是否收藏（简化实现）
        item.setIsBookmarked(false);

        return item;
    }

    /**
     * 将书籍转换为推荐项VO
     */
    private RecommendationResponseVo.RecommendationItemVo convertBookToRecommendationItem(
            Book book, Long userId, Map<String, Double> weaknesses) {

        RecommendationResponseVo.RecommendationItemVo item = new RecommendationResponseVo.RecommendationItemVo();

        item.setId(book.getId());
        item.setTitle(book.getTitle());
        item.setDescription(book.getDescription());
        item.setType("book");
        item.setDifficulty(book.getDifficulty() != null ? book.getDifficulty() : "中等");
        item.setCategory(book.getCategory() != null ? book.getCategory() : "技术专业");
        item.setCover(book.getCover() != null ? book.getCover() : "/static/default-book.jpg");
        item.setRating(book.getRating() != null ? book.getRating() : BigDecimal.valueOf(4.5));
        item.setAuthor(book.getAuthor());
        item.setPageCount(book.getPages());
        item.setPublisher("技术出版社"); // Book实体没有publisher字段，使用默认值
        item.setReaderCount(book.getReadCount() != null ? book.getReadCount() : 0);
        item.setEstimatedTime(calculateBookEstimatedTime(book.getPages()));
        item.setCreateTime(convertToLocalDateTime(book.getCreateTime()));
        item.setUpdateTime(convertToLocalDateTime(book.getUpdateTime()));

        // 设置标签
        if (StrUtil.isNotBlank(book.getTags())) {
            item.setTags(Arrays.asList(book.getTags().split(",")));
        }

        // 计算阅读进度（简化实现）
        item.setReadingProgress(BigDecimal.ZERO);

        // 计算推荐优先级
        Double priorityScore = calculateRecommendationPriority(userId, "book", book.getId());
        item.setPriorityScore(priorityScore);
        item.setPriority(getPriorityLevel(priorityScore));

        // 设置目标能力和推荐原因
        setTargetCapabilityAndReason(item, weaknesses);

        // 设置是否收藏（简化实现）
        item.setIsBookmarked(false);

        return item;
    }

    /**
     * 根据优先级分数获取优先级等级
     */
    private String getPriorityLevel(Double score) {
        if (score >= 80) {
            return "high";
        } else if (score >= 60) {
            return "medium";
        } else {
            return "low";
        }
    }

    /**
     * 设置目标能力和推荐原因
     */
    private void setTargetCapabilityAndReason(RecommendationResponseVo.RecommendationItemVo item,
                                            Map<String, Double> weaknesses) {
        // 简化实现：根据分类设置目标能力
        String category = item.getCategory();
        if (StrUtil.isNotBlank(category)) {
            if (category.contains("技术") || category.contains("专业")) {
                item.setTarget("专业知识");
                item.setTargetCapability("professionalKnowledge");
                item.setImprovementPoints(15);
                item.setRecommendReason("基于您的专业知识薄弱环节推荐");
            } else if (category.contains("沟通") || category.contains("表达")) {
                item.setTarget("表达能力");
                item.setTargetCapability("communicationSkills");
                item.setImprovementPoints(20);
                item.setRecommendReason("基于您的表达能力薄弱环节推荐");
            } else if (category.contains("算法") || category.contains("逻辑")) {
                item.setTarget("逻辑思维");
                item.setTargetCapability("logicalThinking");
                item.setImprovementPoints(18);
                item.setRecommendReason("基于您的逻辑思维能力推荐");
            } else {
                item.setTarget("综合能力");
                item.setTargetCapability("problemSolving");
                item.setImprovementPoints(12);
                item.setRecommendReason("基于您的综合能力提升推荐");
            }
        }
    }

    /**
     * 计算题库预估时间
     */
    private String calculateEstimatedTime(Integer questionCount) {
        if (questionCount == null || questionCount <= 0) {
            return "未知";
        }

        if (questionCount <= 50) {
            return "1-2周";
        } else if (questionCount <= 100) {
            return "2-3周";
        } else if (questionCount <= 200) {
            return "1-2个月";
        } else {
            return "2-3个月";
        }
    }

    /**
     * 计算书籍预估阅读时间
     */
    private String calculateBookEstimatedTime(Integer pageCount) {
        if (pageCount == null || pageCount <= 0) {
            return "未知";
        }

        if (pageCount <= 200) {
            return "1-2周";
        } else if (pageCount <= 400) {
            return "3-4周";
        } else if (pageCount <= 600) {
            return "1-2个月";
        } else {
            return "2-3个月";
        }
    }

    /**
     * 转换Date到LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDateTime();
    }

    /**
     * 转换难度等级（数字转文字）
     */
    private String convertDifficultyLevel(Integer difficulty) {
        if (difficulty == null) {
            return "中等";
        }
        return switch (difficulty) {
            case 1 -> "简单";
            case 2 -> "中等";
            case 3 -> "困难";
            default -> "中等";
        };
    }
}
