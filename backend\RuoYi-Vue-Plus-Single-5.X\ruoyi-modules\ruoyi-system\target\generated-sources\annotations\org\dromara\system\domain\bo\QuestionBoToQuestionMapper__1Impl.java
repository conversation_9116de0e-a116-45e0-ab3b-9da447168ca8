package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.common.mybatis.core.domain.Question;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T09:34:24+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class QuestionBoToQuestionMapper__1Impl implements QuestionBoToQuestionMapper__1 {

    @Override
    public Question convert(QuestionBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Question question = new Question();

        question.setCreateBy( arg0.getCreateBy() );
        question.setCreateDept( arg0.getCreateDept() );
        question.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            question.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        question.setSearchValue( arg0.getSearchValue() );
        question.setUpdateBy( arg0.getUpdateBy() );
        question.setUpdateTime( arg0.getUpdateTime() );
        question.setAcceptanceRate( arg0.getAcceptanceRate() );
        question.setAnalysis( arg0.getAnalysis() );
        question.setAnswer( arg0.getAnswer() );
        question.setBankId( arg0.getBankId() );
        question.setCategory( arg0.getCategory() );
        question.setCommentCount( arg0.getCommentCount() );
        question.setContent( arg0.getContent() );
        question.setCorrectRate( arg0.getCorrectRate() );
        question.setDescription( arg0.getDescription() );
        question.setDifficulty( arg0.getDifficulty() );
        question.setPracticeCount( arg0.getPracticeCount() );
        question.setQuestionCode( arg0.getQuestionCode() );
        question.setQuestionId( arg0.getQuestionId() );
        question.setRemark( arg0.getRemark() );
        question.setSort( arg0.getSort() );
        question.setStatus( arg0.getStatus() );
        question.setTags( arg0.getTags() );
        question.setTitle( arg0.getTitle() );
        question.setType( arg0.getType() );

        return question;
    }

    @Override
    public Question convert(QuestionBo arg0, Question arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAcceptanceRate( arg0.getAcceptanceRate() );
        arg1.setAnalysis( arg0.getAnalysis() );
        arg1.setAnswer( arg0.getAnswer() );
        arg1.setBankId( arg0.getBankId() );
        arg1.setCategory( arg0.getCategory() );
        arg1.setCommentCount( arg0.getCommentCount() );
        arg1.setContent( arg0.getContent() );
        arg1.setCorrectRate( arg0.getCorrectRate() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setQuestionCode( arg0.getQuestionCode() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTags( arg0.getTags() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setType( arg0.getType() );

        return arg1;
    }
}
