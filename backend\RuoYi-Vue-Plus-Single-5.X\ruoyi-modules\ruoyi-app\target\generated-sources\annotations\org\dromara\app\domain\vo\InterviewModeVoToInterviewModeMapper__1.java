package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewMode;
import org.dromara.app.domain.InterviewModeToInterviewModeVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {InterviewModeToInterviewModeVoMapper__1.class},
    imports = {}
)
public interface InterviewModeVoToInterviewModeMapper__1 extends BaseMapper<InterviewModeVo, InterviewMode> {
}
