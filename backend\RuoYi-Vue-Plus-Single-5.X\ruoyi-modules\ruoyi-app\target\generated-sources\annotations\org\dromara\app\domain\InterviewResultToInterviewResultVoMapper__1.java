package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.InterviewResultBoToInterviewResultMapper__1;
import org.dromara.app.domain.vo.InterviewResultVo;
import org.dromara.app.domain.vo.InterviewResultVoToInterviewResultMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {InterviewResultVoToInterviewResultMapper__1.class,InterviewResultBoToInterviewResultMapper__1.class},
    imports = {}
)
public interface InterviewResultToInterviewResultVoMapper__1 extends BaseMapper<InterviewResult, InterviewResultVo> {
}
