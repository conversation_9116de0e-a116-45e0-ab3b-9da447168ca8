package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.VideoComment;
import org.dromara.app.domain.VideoCommentToVideoCommentVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {VideoCommentToVideoCommentVoMapper__1.class,VideoCommentToVideoCommentVoMapper__1.class},
    imports = {}
)
public interface VideoCommentVoToVideoCommentMapper__1 extends BaseMapper<VideoCommentVo, VideoComment> {
}
