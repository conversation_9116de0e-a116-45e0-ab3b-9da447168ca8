package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__144;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.Question;
import org.dromara.common.mybatis.core.domain.QuestionToQuestionVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__144.class,
    uses = {QuestionToQuestionVoMapper__1.class},
    imports = {}
)
public interface QuestionVoToQuestionMapper__1 extends BaseMapper<QuestionVo, Question> {
}
