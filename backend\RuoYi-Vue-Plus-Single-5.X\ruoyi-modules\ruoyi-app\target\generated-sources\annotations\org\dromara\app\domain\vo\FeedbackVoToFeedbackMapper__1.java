package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.Feedback;
import org.dromara.app.domain.FeedbackToFeedbackVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {FeedbackToFeedbackVoMapper__1.class},
    imports = {}
)
public interface FeedbackVoToFeedbackMapper__1 extends BaseMapper<FeedbackVo, Feedback> {
}
