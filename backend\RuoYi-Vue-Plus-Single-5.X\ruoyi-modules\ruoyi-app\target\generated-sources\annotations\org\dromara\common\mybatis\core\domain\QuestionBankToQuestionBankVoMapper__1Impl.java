package org.dromara.common.mybatis.core.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.QuestionBankVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T09:34:34+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class QuestionBankToQuestionBankVoMapper__1Impl implements QuestionBankToQuestionBankVoMapper__1 {

    @Override
    public QuestionBankVo convert(QuestionBank arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionBankVo questionBankVo = new QuestionBankVo();

        questionBankVo.setBankCode( arg0.getBankCode() );
        questionBankVo.setBankId( arg0.getBankId() );
        questionBankVo.setCategories( arg0.getCategories() );
        questionBankVo.setColor( arg0.getColor() );
        questionBankVo.setCreateTime( arg0.getCreateTime() );
        questionBankVo.setDescription( arg0.getDescription() );
        questionBankVo.setDifficulty( arg0.getDifficulty() );
        questionBankVo.setIcon( arg0.getIcon() );
        questionBankVo.setIsBookmarked( arg0.getIsBookmarked() );
        questionBankVo.setMajorId( arg0.getMajorId() );
        questionBankVo.setPracticeCount( arg0.getPracticeCount() );
        questionBankVo.setProgress( arg0.getProgress() );
        questionBankVo.setRemark( arg0.getRemark() );
        questionBankVo.setSort( arg0.getSort() );
        questionBankVo.setStatus( arg0.getStatus() );
        questionBankVo.setTitle( arg0.getTitle() );
        questionBankVo.setTotalQuestions( arg0.getTotalQuestions() );
        questionBankVo.setUpdateTime( arg0.getUpdateTime() );

        return questionBankVo;
    }

    @Override
    public QuestionBankVo convert(QuestionBank arg0, QuestionBankVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBankCode( arg0.getBankCode() );
        arg1.setBankId( arg0.getBankId() );
        arg1.setCategories( arg0.getCategories() );
        arg1.setColor( arg0.getColor() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setIsBookmarked( arg0.getIsBookmarked() );
        arg1.setMajorId( arg0.getMajorId() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setTotalQuestions( arg0.getTotalQuestions() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
