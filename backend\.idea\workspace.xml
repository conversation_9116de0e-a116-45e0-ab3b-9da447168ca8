<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e231f2c9-3c03-4727-9b0e-a28dafc3c073" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/LearningController.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/LearningController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/Question.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/QuestionBank.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/QuestionComment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionBankVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionBankVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionCommentMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionCommentMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/AiEvaluationServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/AiEvaluationServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/resources/mapper/app/QuestionMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/resources/mapper/app/QuestionMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/.eslintrc-auto-import.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/.eslintrc-auto-import.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/pnpm-lock.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/pnpm-lock.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/pages.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/pages.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/pages/learning/question-bank-detail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/pages/learning/question-bank-detail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/pages/learning/question-detail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/pages/learning/question-detail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/service/dataManager.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/service/dataManager.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/service/interview-select.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/service/interview-select.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/service/question.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/service/question.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/auto-import.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/auto-import.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/onboarding.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/onboarding.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/uni-pages.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/uni-pages.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/tsconfig.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/tsconfig.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/vite.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/vite.config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../target/classes/META-INF/mps/autoMapper" beforeDir="false" afterPath="$PROJECT_DIR$/../target/classes/META-INF/mps/autoMapper" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../target/classes/META-INF/mps/mappers" beforeDir="false" afterPath="$PROJECT_DIR$/../target/classes/META-INF/mps/mappers" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30g6d7k07sR3ffn9XX2yhtgcZDM" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.DromaraApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/softwart-xunfei-code2/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Spring Boot.DromaraApplication">
    <configuration name="DromaraApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-admin" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.DromaraApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="jdk-17.0.10-corretto-17.0.10-f644763e9732-24fca987" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e231f2c9-3c03-4727-9b0e-a28dafc3c073" name="更改" comment="" />
      <created>1754036953775</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754036953775</updated>
      <workItem from="1754036954851" duration="476000" />
      <workItem from="1754052301428" duration="5233000" />
      <workItem from="1754098148757" duration="505000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>