package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.QuestionBank;
import org.dromara.common.mybatis.core.domain.QuestionBankToQuestionBankVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {QuestionBankToQuestionBankVoMapper__1.class},
    imports = {}
)
public interface QuestionBankVoToQuestionBankMapper__1 extends BaseMapper<QuestionBankVo, QuestionBank> {
}
