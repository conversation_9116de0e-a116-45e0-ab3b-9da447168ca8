package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T09:34:34+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class KnowledgeBaseToKnowledgeBaseVoMapper__1Impl implements KnowledgeBaseToKnowledgeBaseVoMapper__1 {

    @Override
    public KnowledgeBaseVo convert(KnowledgeBase arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeBaseVo knowledgeBaseVo = new KnowledgeBaseVo();

        knowledgeBaseVo.setCreateBy( arg0.getCreateBy() );
        knowledgeBaseVo.setCreateDept( arg0.getCreateDept() );
        if ( arg0.getCreateTime() != null ) {
            knowledgeBaseVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        knowledgeBaseVo.setDescription( arg0.getDescription() );
        knowledgeBaseVo.setDocumentCount( arg0.getDocumentCount() );
        knowledgeBaseVo.setExtendConfig( arg0.getExtendConfig() );
        knowledgeBaseVo.setId( arg0.getId() );
        knowledgeBaseVo.setIndexConfig( arg0.getIndexConfig() );
        knowledgeBaseVo.setLastSyncTime( arg0.getLastSyncTime() );
        knowledgeBaseVo.setName( arg0.getName() );
        knowledgeBaseVo.setRemark( arg0.getRemark() );
        knowledgeBaseVo.setSortOrder( arg0.getSortOrder() );
        knowledgeBaseVo.setStatus( arg0.getStatus() );
        knowledgeBaseVo.setType( arg0.getType() );
        knowledgeBaseVo.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            knowledgeBaseVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        knowledgeBaseVo.setVectorCount( arg0.getVectorCount() );
        knowledgeBaseVo.setVectorDimension( arg0.getVectorDimension() );

        return knowledgeBaseVo;
    }

    @Override
    public KnowledgeBaseVo convert(KnowledgeBase arg0, KnowledgeBaseVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setDescription( arg0.getDescription() );
        arg1.setDocumentCount( arg0.getDocumentCount() );
        arg1.setExtendConfig( arg0.getExtendConfig() );
        arg1.setId( arg0.getId() );
        arg1.setIndexConfig( arg0.getIndexConfig() );
        arg1.setLastSyncTime( arg0.getLastSyncTime() );
        arg1.setName( arg0.getName() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setType( arg0.getType() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setVectorCount( arg0.getVectorCount() );
        arg1.setVectorDimension( arg0.getVectorDimension() );

        return arg1;
    }
}
