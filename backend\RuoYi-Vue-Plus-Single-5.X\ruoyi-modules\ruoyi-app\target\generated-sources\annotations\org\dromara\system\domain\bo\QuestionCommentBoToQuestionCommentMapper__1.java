package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.QuestionComment;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {},
    imports = {}
)
public interface QuestionCommentBoToQuestionCommentMapper__1 extends BaseMapper<QuestionCommentBo, QuestionComment> {
}
