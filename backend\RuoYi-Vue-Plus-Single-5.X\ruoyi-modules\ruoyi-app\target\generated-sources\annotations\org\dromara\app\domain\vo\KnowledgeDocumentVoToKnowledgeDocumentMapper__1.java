package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeDocument;
import org.dromara.app.domain.KnowledgeDocumentToKnowledgeDocumentVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {KnowledgeDocumentToKnowledgeDocumentVoMapper__1.class},
    imports = {}
)
public interface KnowledgeDocumentVoToKnowledgeDocumentMapper__1 extends BaseMapper<KnowledgeDocumentVo, KnowledgeDocument> {
}
