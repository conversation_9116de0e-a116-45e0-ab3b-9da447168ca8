package org.dromara.app.service;

import org.dromara.app.domain.dto.QuestionBankQueryDto;
import org.dromara.app.domain.dto.QuestionQueryDto;
import org.dromara.app.domain.vo.*;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
 * 学习资源Service接口
 *
 * <AUTHOR>
 */
public interface ILearningService {

    /**
     * 获取专业列表
     *
     * @return 专业列表
     */
    List<MajorVo> getMajorList();

    /**
     * 获取题库列表
     *
     * @param queryDto 查询参数
     * @return 题库列表
     */
    TableDataInfo<QuestionBankVo> getQuestionBankList(QuestionBankQueryDto queryDto);

    /**
     * 切换题库收藏状态
     *
     * @param userId       用户ID
     * @param bankId       题库ID
     * @param isBookmarked 是否收藏
     * @return 收藏状态
     */
    Map<String, Boolean> toggleBookmark(Long userId, String bankId, Boolean isBookmarked);

    /**
     * 搜索题库
     *
     * @param keyword 关键词
     * @param majorId 专业ID
     * @return 题库列表
     */
    List<QuestionBankVo> searchQuestionBanks(String keyword, String majorId);

    /**
     * 获取题库详情
     *
     * @param bankId  题库ID
     * @param majorId 专业ID
     * @param userId  用户ID
     * @return 题库详情
     */
    QuestionBankVo getQuestionBankDetail(String bankId, String majorId, Long userId);

    /**
     * 获取热门题库
     *
     * @param limit 限制数量
     * @return 题库列表
     */
    List<QuestionBankVo> getHotQuestionBanks(Integer limit);

    /**
     * 获取最新题库
     *
     * @param limit 限制数量
     * @return 题库列表
     */
    List<QuestionBankVo> getNewQuestionBanks(Integer limit);

    /**
     * 获取题库详细信息
     *
     * @param bankId 题库ID
     * @param userId 用户ID
     * @return 题库详细信息
     */
    QuestionBankDetailVO getQuestionBankFullDetail(String bankId, Long userId);

    /**
     * 获取题库分类题目
     *
     * @param bankId 题库ID
     * @return 按分类组织的题目列表
     */
    Map<String, Object> getQuestionsByCategory(String bankId, String category, Integer pageNum, Integer pageSize);

    /**
     * 获取推荐题目
     *
     * @param bankId 题库ID
     * @param limit  限制数量
     * @return 推荐题目列表
     */
    List<QuestionVo> getRecommendedQuestions(String bankId, Integer limit);

    /**
     * 切换题库收藏状态（新版）
     *
     * @param userId 用户ID
     * @param bankId 题库ID
     * @return 收藏结果
     */
    Map<String, Object> toggleQuestionBankBookmark(Long userId, String bankId);

    /**
     * 获取题库题目列表（支持筛选和搜索）
     *
     * @param bankId   题库ID
     * @param queryDto 查询参数
     * @return 题目列表
     */
    TableDataInfo<QuestionVo> getQuestionList(String bankId, QuestionQueryDto queryDto);

    /**
     * 获取题目详情
     *
     * @param questionId 题目ID
     * @param userId     用户ID
     * @return 题目详情
     */
    QuestionDetailVO getQuestionDetail(String questionId, Long userId);

    /**
     * 搜索题目
     *
     * @param bankId     题库ID
     * @param keyword    搜索关键词
     * @param difficulty 难度等级
     * @param category   分类
     * @param completed  完成状态
     * @param userId     用户ID
     * @return 题目列表
     */
    List<QuestionVo> searchQuestions(String bankId, String keyword, String difficulty,
                                     String category, Boolean completed, Long userId);

    /**
     * 切换题目收藏状态
     *
     * @param userId       用户ID
     * @param questionId   题目ID
     * @param isBookmarked 是否收藏
     * @return 收藏状态结果
     */
    Map<String, Object> toggleQuestionBookmark(Long userId, String questionId, Boolean isBookmarked);

    /**
     * 获取题目评论列表
     *
     * @param questionId     题目ID
     * @param page           页码
     * @param pageSize       每页大小
     * @param orderBy        排序字段
     * @param orderDirection 排序方向
     * @return 评论列表
     */
    Map<String, Object> getQuestionComments(String questionId, Integer page, Integer pageSize,
                                            String orderBy, String orderDirection);

    /**
     * 创建题目评论
     *
     * @param userId     用户ID
     * @param questionId 题目ID
     * @param content    评论内容
     * @param parentId   父评论ID
     * @return 创建的评论
     */
    QuestionCommentVO createQuestionComment(Long userId, String questionId, String content, Long parentId);

    /**
     * 删除题目评论
     *
     * @param userId    用户ID
     * @param commentId 评论ID
     * @return 删除结果
     */
    boolean deleteQuestionComment(Long userId, String commentId);

    /**
     * 点赞/取消点赞评论
     *
     * @param userId    用户ID
     * @param commentId 评论ID
     * @return 点赞结果
     */
    Map<String, Object> likeQuestionComment(Long userId, String commentId);

    /**
     * 提交题目练习记录
     *
     * @param userId     用户ID
     * @param questionId 题目ID
     * @param userAnswer 用户答案
     * @param timeSpent  用时（秒）
     * @return 提交结果
     */
    Map<String, Object> submitPracticeRecord(Long userId, String questionId, String userAnswer, Integer timeSpent);

    /**
     * 获取题目统计信息
     *
     * @param questionId 题目ID
     * @return 统计信息
     */
    Map<String, Object> getQuestionStats(String questionId);

    /**
     * 获取相关题目推荐
     *
     * @param questionId 题目ID
     * @param limit      推荐数量限制
     * @return 相关题目列表
     */
    List<QuestionDetailVO> getRelatedQuestions(String questionId, Integer limit);

    /**
     * 举报题目或评论
     *
     * @param userId      用户ID
     * @param targetId    目标ID
     * @param targetType  目标类型（question/comment）
     * @param reason      举报原因
     * @param description 详细描述
     * @return 举报结果
     */
    boolean reportContent(Long userId, String targetId, String targetType, String reason, String description);

    /**
     * 获取学习统计数据
     *
     * @param userId 用户ID
     * @return 学习统计数据
     */
    Map<String, Object> getLearningStats(Long userId);

    /**
     * 获取今日推荐内容
     *
     * @param userId 用户ID
     * @return 今日推荐内容
     */
    Map<String, Object> getTodayRecommendation(Long userId);

    /**
     * 获取资源分类统计数据
     *
     * @param majorId 专业ID（可选）
     * @param userId  用户ID（可选）
     * @return 资源分类统计数据
     */
    List<Map<String, Object>> getResourceCategoryStats(String majorId, Long userId);

    /**
     * 获取题库题目统计信息
     *
     * @param bankId 题库ID
     * @return 统计信息
     */
    Map<String, Object> getQuestionStatistics(String bankId);

    /**
     * 获取专业下的所有题库列表（增强版 - 支持更多筛选和排序）
     *
     * @param queryDto 查询参数
     * @param userId   用户ID
     * @return 题库列表
     */
    TableDataInfo<QuestionBankVo> getMajorQuestionBankList(QuestionBankQueryDto queryDto, Long userId);

    /**
     * 获取专业题库统计信息
     *
     * @param majorId 专业ID
     * @param userId  用户ID
     * @return 统计信息
     */
    Map<String, Object> getMajorQuestionBankStatistics(String majorId, Long userId);

    /**
     * 获取专业题库筛选选项计数
     *
     * @param majorId 专业ID
     * @param userId  用户ID
     * @return 筛选选项计数
     */
    Map<String, Object> getMajorQuestionBankFilterCounts(String majorId, Long userId);
}
