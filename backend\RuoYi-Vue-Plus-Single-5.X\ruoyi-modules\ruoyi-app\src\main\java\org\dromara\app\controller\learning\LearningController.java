package org.dromara.app.controller.learning;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.dto.QuestionBankQueryDto;
import org.dromara.app.domain.dto.QuestionQueryDto;
import org.dromara.app.domain.vo.*;
import org.dromara.app.service.ILearningService;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学习资源控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/learning")
public class LearningController {

    private final ILearningService learningService;

    /**
     * 获取专业列表
     *
     * @return 专业列表响应
     */
    @GetMapping("/majors")
    public R<List<MajorVo>> getMajorList() {
        try {
            List<MajorVo> majorList = learningService.getMajorList();
            return R.ok(majorList);
        } catch (Exception e) {
            log.error("获取专业列表失败：{}", e.getMessage());
            return R.fail("获取专业列表失败");
        }
    }

    /**
     * 获取题库列表
     *
     * @param queryDto 查询参数
     * @return 题库列表响应
     */
    @GetMapping("/question-banks")
    public R<Map<String, Object>> getQuestionBankList(@Valid QuestionBankQueryDto queryDto) {
        try {
            TableDataInfo<QuestionBankVo> tableDataInfo = learningService.getQuestionBankList(queryDto);

            Map<String, Object> result = new HashMap<>();
            result.put("list", tableDataInfo.getRows());
            result.put("total", tableDataInfo.getTotal());
            result.put("page", queryDto.getPage());
            result.put("pageSize", queryDto.getPageSize());

            return R.ok(result);
        } catch (Exception e) {
            log.error("获取题库列表失败：{}", e.getMessage());
            return R.fail("获取题库列表失败");
        }
    }

    /**
     * 切换题库收藏状态
     *
     * @param request 收藏请求参数
     * @return 收藏状态响应
     */
    @PostMapping("/question-banks/bookmark")
    public R<Map<String, Boolean>> toggleBookmark(@RequestBody @Valid Map<String, Object> request) {
        try {
            String bankId = (String) request.get("bankId");
            Boolean isBookmarked = (Boolean) request.get("isBookmarked");

            if (StrUtil.isBlank(bankId) || isBookmarked == null) {
                return R.fail("参数不完整");
            }

            Long userId = StpUtil.getLoginIdAsLong();
            Map<String, Boolean> result = learningService.toggleBookmark(userId, bankId, isBookmarked);

            return R.ok(result);
        } catch (Exception e) {
            log.error("切换收藏状态失败：{}", e.getMessage());
            return R.fail("操作失败，请稍后重试");
        }
    }

    /**
     * 搜索题库
     *
     * @param keyword 搜索关键词
     * @param majorId 专业ID
     * @return 题库列表响应
     */
    @GetMapping("/question-banks/search")
    public R<List<QuestionBankVo>> searchQuestionBanks(
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) String majorId) {
        try {
            List<QuestionBankVo> banks = learningService.searchQuestionBanks(keyword, majorId);
            return R.ok(banks);
        } catch (Exception e) {
            log.error("搜索题库失败：{}", e.getMessage());
            return R.fail("搜索失败，请稍后重试");
        }
    }

    /**
     * 获取题库详情
     *
     * @param bankId  题库ID
     * @param majorId 专业ID
     * @return 题库详情响应
     */
    @GetMapping("/question-banks/{bankId}")
    public R<QuestionBankVo> getQuestionBankDetail(
        @PathVariable String bankId,
        @RequestParam(required = false) String majorId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            QuestionBankVo detail = learningService.getQuestionBankDetail(bankId, majorId, userId);
            if (detail == null) {
                return R.fail("题库不存在");
            }

            return R.ok(detail);
        } catch (Exception e) {
            log.error("获取题库详情失败：{}", e.getMessage());
            return R.fail("获取详情失败");
        }
    }

    /**
     * 获取热门题库
     *
     * @param limit 限制数量
     * @return 热门题库列表响应
     */
    @GetMapping("/question-banks/hot")
    public R<List<QuestionBankVo>> getHotQuestionBanks(@RequestParam(required = false) Integer limit) {
        try {
            List<QuestionBankVo> banks = learningService.getHotQuestionBanks(limit);
            return R.ok(banks);
        } catch (Exception e) {
            log.error("获取热门题库失败：{}", e.getMessage());
            return R.fail("获取热门题库失败");
        }
    }

    /**
     * 获取最新题库
     *
     * @param limit 限制数量
     * @return 最新题库列表响应
     */
    @GetMapping("/question-banks/new")
    public R<List<QuestionBankVo>> getNewQuestionBanks(@RequestParam(required = false) Integer limit) {
        try {
            List<QuestionBankVo> banks = learningService.getNewQuestionBanks(limit);
            return R.ok(banks);
        } catch (Exception e) {
            log.error("获取最新题库失败：{}", e.getMessage());
            return R.fail("获取最新题库失败");
        }
    }

    /**
     * 获取题库完整详情（包含学习进度等信息）
     *
     * @param bankId 题库ID
     * @return 题库完整详情响应
     */
    @GetMapping("/question-banks/{bankId}/detail")
    public R<QuestionBankDetailVO> getQuestionBankFullDetail(@PathVariable String bankId) {
        try {
            Long userId = null;
            try {
                userId = StpUtil.getLoginIdAsLong();
            } catch (Exception ignored) {
                // 未登录用户
            }

            QuestionBankDetailVO detail = learningService.getQuestionBankFullDetail(bankId, userId);
            if (detail == null) {
                return R.fail("题库不存在");
            }

            return R.ok(detail);
        } catch (Exception e) {
            log.error("获取题库详情失败：{}", e.getMessage(), e);
            return R.fail("获取详情失败");
        }
    }

    /**
     * 获取题库分类题目
     *
     * @param bankId 题库ID
     * @return 按分类组织的题目列表响应
     */
    @GetMapping("/question-banks/{bankId}/questions-by-category")
    public R<Map<String, Object>> getQuestionsByCategory(@PathVariable String bankId,
                                                         @RequestParam(required = false) String category,
                                                         @RequestParam(required = false) Integer pageNum,
                                                         @RequestParam(required = false) Integer pageSize) {
        try {
            Map<String, Object> questions = learningService.getQuestionsByCategory(bankId, category, pageNum, pageSize);
            return R.ok(questions);
        } catch (Exception e) {
            log.error("获取分类题目失败：{}", e.getMessage(), e);
            return R.fail("获取题目失败");
        }
    }

    /**
     * 获取推荐题目
     *
     * @param bankId 题库ID
     * @param limit  限制数量
     * @return 推荐题目列表响应
     */
    @GetMapping("/question-banks/{bankId}/recommended-questions")
    public R<List<QuestionVo>> getRecommendedQuestions(
        @PathVariable String bankId,
        @RequestParam(required = false, defaultValue = "5") Integer limit) {
        try {
            List<QuestionVo> questions = learningService.getRecommendedQuestions(bankId, limit);
            return R.ok(questions);
        } catch (Exception e) {
            log.error("获取推荐题目失败：{}", e.getMessage(), e);
            return R.fail("获取推荐题目失败");
        }
    }

    /**
     * 切换题库收藏状态（新版）
     *
     * @param bankId 题库ID
     * @return 收藏状态响应
     */
    @PostMapping("/question-banks/{bankId}/toggle-bookmark")
    public R<Map<String, Object>> toggleQuestionBankBookmark(@PathVariable String bankId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            Map<String, Object> result = learningService.toggleQuestionBankBookmark(userId, bankId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("切换收藏状态失败：{}", e.getMessage(), e);
            return R.fail("操作失败，请稍后重试");
        }
    }

    /**
     * 获取题库题目列表（支持筛选和搜索）
     *
     * @param bankId   题库ID
     * @param queryDto 查询参数
     * @return 题目列表响应
     */
    @GetMapping("/question-banks/{bankId}/questions")
    public R<Map<String, Object>> getQuestionList(
        @PathVariable String bankId,
        @Valid QuestionQueryDto queryDto) {
        try {
            TableDataInfo<QuestionVo> tableDataInfo = learningService.getQuestionList(bankId, queryDto);

            Map<String, Object> result = new HashMap<>();
            result.put("list", tableDataInfo.getRows());
            result.put("total", tableDataInfo.getTotal());
            result.put("page", queryDto.getPageNum());
            result.put("pageSize", queryDto.getPageSize());

            return R.ok(result);
        } catch (Exception e) {
            log.error("获取题目列表失败：{}", e.getMessage(), e);
            return R.fail("获取题目列表失败");
        }
    }

    /**
     * 获取题库题目统计信息
     *
     * @param bankId 题库ID
     * @return 统计信息响应
     */
    @GetMapping("/question-banks/{bankId}/statistics")
    public R<Map<String, Object>> getQuestionStatistics(@PathVariable String bankId) {
        try {
            Map<String, Object> statistics = learningService.getQuestionStatistics(bankId);
            return R.ok(statistics);
        } catch (Exception e) {
            log.error("获取题目统计信息失败：{}", e.getMessage(), e);
            return R.fail("获取统计信息失败");
        }
    }

    /**
     * 获取题目详情
     *
     * @param questionId 题目ID
     * @return 题目详情响应
     */
    @GetMapping("/questions/{questionId}")
    public R<QuestionDetailVO> getQuestionDetail(@PathVariable String questionId) {
        try {
            Long userId = null;
            try {
                userId = StpUtil.getLoginIdAsLong();
            } catch (Exception ignored) {
                // 未登录用户
            }

            QuestionDetailVO detail = learningService.getQuestionDetail(questionId, userId);
            if (detail == null) {
                return R.fail("题目不存在");
            }

            return R.ok(detail);
        } catch (Exception e) {
            log.error("获取题目详情失败：{}", e.getMessage(), e);
            return R.fail("获取题目详情失败");
        }
    }

    /**
     * 搜索题目
     *
     * @param bankId     题库ID
     * @param keyword    搜索关键词
     * @param difficulty 难度等级
     * @param category   分类
     * @param completed  完成状态
     * @return 题目列表响应
     */
    @GetMapping("/question-banks/{bankId}/questions/search")
    public R<List<QuestionVo>> searchQuestions(
        @PathVariable String bankId,
        @RequestParam(required = false) String keyword,
        @RequestParam(required = false) String difficulty,
        @RequestParam(required = false) String category,
        @RequestParam(required = false) Boolean completed) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            ;
            List<QuestionVo> questions = learningService.searchQuestions(bankId, keyword, difficulty, category, completed, userId);
            return R.ok(questions);
        } catch (Exception e) {
            log.error("搜索题目失败：{}", e.getMessage(), e);
            return R.fail("搜索题目失败");
        }
    }

    /**
     * 切换题目收藏状态
     *
     * @param questionId 题目ID
     * @param request    收藏请求参数
     * @return 收藏状态响应
     */
    @PostMapping("/questions/{questionId}/bookmark")
    public R<Map<String, Object>> toggleQuestionBookmark(
        @PathVariable String questionId,
        @RequestBody @Valid Map<String, Object> request) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            Boolean isBookmarked = (Boolean) request.get("isBookmarked");

            if (isBookmarked == null) {
                return R.fail("参数不完整");
            }

            Map<String, Object> result = learningService.toggleQuestionBookmark(userId, questionId, isBookmarked);
            return R.ok(result);
        } catch (Exception e) {
            log.error("切换题目收藏状态失败：{}", e.getMessage(), e);
            return R.fail("操作失败，请稍后重试");
        }
    }

    /**
     * 获取题目评论列表
     *
     * @param questionId     题目ID
     * @param page           页码
     * @param pageSize       每页大小
     * @param orderBy        排序字段
     * @param orderDirection 排序方向
     * @return 评论列表响应
     */
    @GetMapping("/questions/{questionId}/comments")
    public R<Map<String, Object>> getQuestionComments(
        @PathVariable String questionId,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "10") Integer pageSize,
        @RequestParam(required = false, defaultValue = "createTime") String orderBy,
        @RequestParam(required = false, defaultValue = "desc") String orderDirection) {
        try {
            Map<String, Object> result = learningService.getQuestionComments(
                questionId, page, pageSize, orderBy, orderDirection);
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取题目评论失败：{}", e.getMessage(), e);
            return R.fail("获取评论失败");
        }
    }

    /**
     * 创建题目评论
     *
     * @param questionId 题目ID
     * @param request    评论创建请求
     * @return 创建结果响应
     */
    @PostMapping("/questions/{questionId}/comments")
    public R<QuestionCommentVO> createQuestionComment(
        @PathVariable String questionId,
        @RequestBody @Valid Map<String, Object> request) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            String content = (String) request.get("content");
            String parentIdStr = (String) request.get("parentId");

            if (StrUtil.isBlank(content)) {
                return R.fail("评论内容不能为空");
            }

            Long parentId = StrUtil.isNotBlank(parentIdStr) ? Long.parseLong(parentIdStr) : null;

            QuestionCommentVO comment = learningService.createQuestionComment(
                userId, questionId, content, parentId);
            return R.ok(comment);
        } catch (Exception e) {
            log.error("创建题目评论失败：{}", e.getMessage(), e);
            return R.fail("评论失败，请稍后重试");
        }
    }

    /**
     * 获取专业下的所有题库列表（增强版 - 支持更多筛选和排序）
     *
     * @param queryDto 查询参数
     * @return 题库列表响应
     */
    @GetMapping("/majors/{majorId}/question-banks")
    public R<Map<String, Object>> getMajorQuestionBankList(
        @PathVariable String majorId,
        @Valid QuestionBankQueryDto queryDto) {
        try {
            // 设置专业ID
            queryDto.setMajorId(majorId);

            Long userId = null;
            try {
                userId = StpUtil.getLoginIdAsLong();
            } catch (Exception ignored) {
                // 未登录用户
            }

            TableDataInfo<QuestionBankVo> tableDataInfo = learningService.getMajorQuestionBankList(queryDto, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("list", tableDataInfo.getRows());
            result.put("total", tableDataInfo.getTotal());
            result.put("page", queryDto.getPage());
            result.put("pageSize", queryDto.getPageSize());

            return R.ok(result);
        } catch (Exception e) {
            log.error("获取专业题库列表失败：{}", e.getMessage(), e);
            return R.fail("获取题库列表失败");
        }
    }

    /**
     * 获取专业题库统计信息
     *
     * @param majorId 专业ID
     * @return 统计信息响应
     */
    @GetMapping("/majors/{majorId}/question-banks/statistics")
    public R<Map<String, Object>> getMajorQuestionBankStatistics(@PathVariable String majorId) {
        try {
            Long userId = null;
            try {
                userId = StpUtil.getLoginIdAsLong();
            } catch (Exception ignored) {
                // 未登录用户
            }

            Map<String, Object> statistics = learningService.getMajorQuestionBankStatistics(majorId, userId);
            return R.ok(statistics);
        } catch (Exception e) {
            log.error("获取专业题库统计信息失败：{}", e.getMessage(), e);
            return R.fail("获取统计信息失败");
        }
    }

    /**
     * 获取专业题库筛选选项计数
     *
     * @param majorId 专业ID
     * @return 筛选选项计数响应
     */
    @GetMapping("/majors/{majorId}/question-banks/filter-counts")
    public R<Map<String, Object>> getMajorQuestionBankFilterCounts(@PathVariable String majorId) {
        try {
            Long userId = null;
            try {
                userId = StpUtil.getLoginIdAsLong();
            } catch (Exception ignored) {
                // 未登录用户
            }

            Map<String, Object> filterCounts = learningService.getMajorQuestionBankFilterCounts(majorId, userId);
            return R.ok(filterCounts);
        } catch (Exception e) {
            log.error("获取题库筛选计数失败：{}", e.getMessage(), e);
            return R.fail("获取筛选计数失败");
        }
    }

    /**
     * 重置专业题库筛选条件
     *
     * @param majorId 专业ID
     * @return 重置结果响应
     */
    @PostMapping("/majors/{majorId}/question-banks/reset-filters")
    public R<Map<String, Object>> resetMajorQuestionBankFilters(@PathVariable String majorId) {
        try {
            // 构建默认查询参数
            QuestionBankQueryDto queryDto = new QuestionBankQueryDto();
            queryDto.setMajorId(majorId);
            queryDto.setFilter("all");
            queryDto.setSortType("default");
            queryDto.setKeyword("");
            queryDto.setPage(1);
            queryDto.setPageSize(10);

            Long userId = null;
            try {
                userId = StpUtil.getLoginIdAsLong();
            } catch (Exception ignored) {
                // 未登录用户
            }

            TableDataInfo<QuestionBankVo> tableDataInfo = learningService.getMajorQuestionBankList(queryDto, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("list", tableDataInfo.getRows());
            result.put("total", tableDataInfo.getTotal());
            result.put("page", queryDto.getPage());
            result.put("pageSize", queryDto.getPageSize());
            result.put("message", "筛选条件已重置");

            return R.ok(result);
        } catch (Exception e) {
            log.error("重置题库筛选条件失败：{}", e.getMessage(), e);
            return R.fail("重置筛选条件失败");
        }
    }
    /**
     * 获取学习统计数据
     *
     * @return 学习统计响应
     */
    @GetMapping("/stats")
    public R<Map<String, Object>> getLearningStats() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            Map<String, Object> stats = learningService.getLearningStats(userId);
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取学习统计失败：{}", e.getMessage(), e);
            return R.fail("获取学习统计失败");
        }
    }

    /**
     * 获取今日推荐内容
     *
     * @return 今日推荐响应
     */
    @GetMapping("/today-recommendation")
    public R<Map<String, Object>> getTodayRecommendation() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            Map<String, Object> recommendation = learningService.getTodayRecommendation(userId);
            return R.ok(recommendation);
        } catch (Exception e) {
            log.error("获取今日推荐失败：{}", e.getMessage(), e);
            return R.fail("获取今日推荐失败");
        }
    }

    /**
     * 获取资源分类统计数据
     *
     * @param majorId 专业ID（可选）
     * @return 资源分类统计响应
     */
    @GetMapping("/resource-category-stats")
    public R<List<Map<String, Object>>> getResourceCategoryStats(
        @RequestParam(required = false) String majorId) {
        try {
            Long userId = null;
            try {
                userId = StpUtil.getLoginIdAsLong();
            } catch (Exception ignored) {
                // 未登录用户
            }

            List<Map<String, Object>> stats = learningService.getResourceCategoryStats(majorId, userId);
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取资源分类统计失败：{}", e.getMessage(), e);
            return R.fail("获取资源分类统计失败");
        }
    }
}
