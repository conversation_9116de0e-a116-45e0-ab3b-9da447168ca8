package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.UserResumeBoToUserResumeMapper__1;
import org.dromara.app.domain.vo.UserResumeVo;
import org.dromara.app.domain.vo.UserResumeVoToUserResumeMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {UserResumeVoToUserResumeMapper__1.class,UserResumeBoToUserResumeMapper__1.class},
    imports = {}
)
public interface UserResumeToUserResumeVoMapper__1 extends BaseMapper<UserResume, UserResumeVo> {
}
