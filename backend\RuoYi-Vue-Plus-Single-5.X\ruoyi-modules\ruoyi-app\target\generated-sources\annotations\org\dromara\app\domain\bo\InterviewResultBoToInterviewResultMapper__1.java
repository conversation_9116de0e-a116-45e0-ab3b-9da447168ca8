package org.dromara.app.domain.bo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewResult;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {},
    imports = {}
)
public interface InterviewResultBoToInterviewResultMapper__1 extends BaseMapper<InterviewResultBo, InterviewResult> {
}
