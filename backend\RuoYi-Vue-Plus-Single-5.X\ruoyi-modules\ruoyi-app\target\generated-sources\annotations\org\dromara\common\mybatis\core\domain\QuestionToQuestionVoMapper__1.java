package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionBoToQuestionMapper__1;
import org.dromara.system.domain.vo.QuestionVo;
import org.dromara.system.domain.vo.QuestionVoToQuestionMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {QuestionBoToQuestionMapper__1.class,QuestionVoToQuestionMapper__1.class},
    imports = {}
)
public interface QuestionToQuestionVoMapper__1 extends BaseMapper<Question, QuestionVo> {
}
