package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__144;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionBankBoToQuestionBankMapper__1;
import org.dromara.system.domain.vo.QuestionBankVo;
import org.dromara.system.domain.vo.QuestionBankVoToQuestionBankMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__144.class,
    uses = {QuestionBankVoToQuestionBankMapper__1.class,QuestionBankBoToQuestionBankMapper__1.class},
    imports = {}
)
public interface QuestionBankToQuestionBankVoMapper__1 extends BaseMapper<QuestionBank, QuestionBankVo> {
}
