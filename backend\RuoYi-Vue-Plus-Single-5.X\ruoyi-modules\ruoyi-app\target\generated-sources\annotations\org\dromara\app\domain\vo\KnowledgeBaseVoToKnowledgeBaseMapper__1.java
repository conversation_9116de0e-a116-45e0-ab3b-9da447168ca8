package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.app.domain.KnowledgeBaseToKnowledgeBaseVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {KnowledgeBaseToKnowledgeBaseVoMapper__1.class},
    imports = {}
)
public interface KnowledgeBaseVoToKnowledgeBaseMapper__1 extends BaseMapper<KnowledgeBaseVo, KnowledgeBase> {
}
