package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__145;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.Video;
import org.dromara.app.domain.VideoToVideoDetailVoMapper__1;
import org.dromara.app.utils.VideoMappingUtils;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__145.class,
    uses = {VideoMappingUtils.class,VideoToVideoDetailVoMapper__1.class},
    imports = {}
)
public interface VideoDetailVoToVideoMapper__1 extends BaseMapper<VideoDetailVo, Video> {
}
